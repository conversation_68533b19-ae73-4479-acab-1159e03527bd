//
//  SampleHandler.m
//  ReplaykitMirror
//
//  Created by wzt on 2019/9/6.
//  Copyright © 2019 carbit. All rights reserved.
//


#import "SampleHandler.h"
#import <eDriveConnectKit/RPBridge.h>

@implementation SampleHandler

- (void)broadcastStartedWithSetupInfo:(NSDictionary<NSString *,NSObject *> *)setupInfo {
    
    // User has requested to start the broadcast. Setup info from the UI extension can be supplied but optional.
    [[RPBridge sharedInstance] startSaveLogWithGroupId:@"group.com.carbit.edrive.neutral"];
    
    [RPBridge sharedInstance].broadcastSampleHandler = self;
}

- (void)broadcastAnnotatedWithApplicationInfo:(NSDictionary *)applicationInfo
{
}

- (void)broadcastPaused {
    // User has requested to pause the broadcast. Samples will stop being delivered.
}

- (void)broadcastResumed {
    // User has requested to resume the broadcast. Samples delivery will resume.
}

- (void)broadcastFinished {
    // User has requested to finish the broadcast.
    NSLog(@"SampleHandler broadcastFinished");
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [[RPBridge sharedInstance] sendReplayKitStatus:NO];
    });
    sleep(2);
}

- (void)processSampleBuffer:(CMSampleBufferRef)sampleBuffer withType:(RPSampleBufferType)sampleBufferType {

    switch (sampleBufferType) {
        case RPSampleBufferTypeVideo:
        // Handle video sample buffer
        {
            [[RPBridge sharedInstance] procesVideoSampleBuffer:sampleBuffer];
        }
            break;
        case RPSampleBufferTypeAudioApp:
            // Handle audio sample buffer for app audio
            break;
        case RPSampleBufferTypeAudioMic:
            // Handle audio sample buffer for mic audio
            break;

        default:
            break;
    }
}

@end
