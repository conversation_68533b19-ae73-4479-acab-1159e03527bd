//
//  CFWidget.swift
//  CFWidget
//
//  Created by yph on 2024/3/13.
//  Copyright © 2024 apple. All rights reserved.
//

import WidgetKit
import SwiftUI

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        let model = CFDefine.proxy_ticket().isEmpty ? CFVehicleWidgetModel() : CFWidgetData.shared.dataModel ?? CFVehicleWidgetModel()
        return SimpleEntry(date: Date(), widgetModel: model)
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let model = CFDefine.proxy_ticket().isEmpty ? CFVehicleWidgetModel() : CFWidgetData.shared.dataModel ?? CFVehicleWidgetModel()
        let entry = SimpleEntry(date: Date(), widgetModel: model)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {

        // Generate a timeline consisting of five entries an hour apart, starting from the current date.
        
        let currentDate = Date()
        let updateDate = Calendar.current.date(byAdding: .second, value: CFWidgetData.shared.randomTime, to: currentDate)!

        if (CFDefine.proxy_ticket().isEmpty) {
            let wModel = CFVehicleWidgetModel()
            CFWidgetData.shared.widgetModel = wModel
            let entry = SimpleEntry(date: currentDate, widgetModel: wModel)
            let timeline = Timeline(entries: [entry], policy: .after(updateDate))
            DispatchQueue.main.async {
                completion(timeline)
            }
        }  else {
            VehicleWidget.request { result in
                let wModel: CFVehicleWidgetModel
                if case.success(let response) = result {
                    wModel = response
                } else {
                    let model = CFDefine.proxy_ticket().isEmpty ? CFVehicleWidgetModel() : CFWidgetData.shared.dataModel ?? CFVehicleWidgetModel()
                    wModel = model
                }
                CFWidgetData.shared.widgetModel = wModel
                let entry = SimpleEntry(date: currentDate, widgetModel: wModel)
                let timeline = Timeline(entries: [entry], policy: .after(updateDate))
                DispatchQueue.main.async {
                    completion(timeline)
                }
            }
        }
    }
}

struct VehicleWidget {
    static func request(completion:@escaping(Result<CFVehicleWidgetModel, Error>) ->Void) {
        CFWidgetVM.getVehicleWidgets { widgetModel in
            completion(.success(widgetModel))
        } fail: { error in
            completion(.failure(error))
        }
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let widgetModel: CFVehicleWidgetModel
}

struct CFWidgetEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        //Text(entry.date, style: .time)
        
        switch family {
        case .systemSmall:
            CFWidgetSmall().widgetURL(URL(string: "widgetUrl://action=small"))
        default:
            CFWidgetMedium().widgetURL(URL(string: "widgetUrl://action=medium"))
        }
    }
}

struct CFWidget: Widget {
    let kind: String = "CFWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            CFWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("我的爱车")
        .description("时时查看车辆状态")
        .supportedFamilies([.systemSmall, .systemMedium]) //支持大小类型
        .disableContentMarginsIfNeeded()
    }
}

struct CFWidget_Previews: PreviewProvider {
    static var previews: some View {
        let model = CFDefine.proxy_ticket().isEmpty ? CFVehicleWidgetModel() : CFWidgetData.shared.dataModel ?? CFVehicleWidgetModel()
        CFWidgetEntryView(entry: SimpleEntry(date: Date(), widgetModel: model))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
    }
}

@available(iOS 14.0.0, *)
extension WidgetConfiguration {
    func disableContentMarginsIfNeeded() -> some WidgetConfiguration {
        if #available(iOS 17.0, *) {
            return self.contentMarginsDisabled()
        } else {
            return self
        }
    }
}
