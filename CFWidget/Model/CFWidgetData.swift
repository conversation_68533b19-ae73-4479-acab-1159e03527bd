//
//  CFWidgetData.swift
//  CFWidget
//
//  Created by yph on 2024/3/13.
//  Copyright © 2024 apple. All rights reserved.
//

import Foundation
import UIKit

class CFWidgetData {
    static let shared = CFWidgetData()
    private init() {}
    
    var widgetModel: CFVehicleWidgetModel?
}

extension CFWidgetData {
    var dataModel: CFVehicleWidgetModel? {
        let model = CFVehicleWidgetModel.yy_model(withJSON: CFDefine.proxy_widgetData())
        return model
    }
    
    var functionArray: Array<Dictionary<String, String>>? {
        let array : NSMutableArray = NSMutableArray()
        
        let lock = self.widgetModel?.checkVehicleFunctionSign12 == 1
        let motoPlay = self.widgetModel?.checkVehicleFunctionSign14 == 1
        let find = self.widgetModel?.checkVehicleFunctionFindVehicle == 1
        let tBoxType = self.widgetModel?.type
        
        let headLockState = self.widgetModel?.headLockState == "0"
        let btnDict1 = ["image" : headLockState ? "widget_unlock" : "widget_lock", "url" : headLockState ? "widgetUrl://action=unlock" : "widgetUrl://action=lock"]
        let btnDict2 = ["image" : "widget_motoPlay", "url" : "widgetUrl://action=motoPlay"]
        let btnDict3 = ["image" : "widget_search", "url" : "widgetUrl://action=search"]
        let btnDict4 = ["image" : "widget_track", "url" : "widgetUrl://action=ride"]
        
        if tBoxType == 1 {
            //一代车：固定为寻车、历史轨迹
            array.add(btnDict3)
            array.add(btnDict4)
        }else if tBoxType == 2 {
            //二代车
            if !motoPlay && !lock {
                //无MotoPlay、无车辆解锁功能：
                array.add(btnDict3)
                array.add(btnDict4)
            }else {
                if lock {
                    array.add(btnDict1)
                }
                if motoPlay {
                    array.add(btnDict2)
                }
                if find {
                    array.add(btnDict3)
                }
            }
        }
        return array as? Array<Dictionary<String, String>>
    }
    
    var hmiRidableMile: String? {
        var hmiRidableMile = self.widgetModel?.hmiRidableMile ?? "0"
        if (hmiRidableMile.isEmpty) {
            hmiRidableMile = "0"
        }
        let mile = Int(hmiRidableMile)!
        if (mile < 0) {
            hmiRidableMile = "--"
        } else if (mile >= 0 && mile < 10) {
            hmiRidableMile = "<10"
        }
        
        if (self.widgetModel?.simRemainingDays == 0) {
            hmiRidableMile = "--"
        }
        return hmiRidableMile
    }
    
    var randomTime:Int {
        let random = Int.random(in: 0...600) + 1500
        return random;
    }
    
    var time:String {
        let currentDate = Date()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm"
        let currentDateString = dateFormatter.string(from: currentDate)
        return currentDateString;
    }
    
    var lockState : String {
        //锁具状态
        var lockState = ""
        if (self.widgetModel?.headLockState == "0") {
            lockState = "已开锁"
        }else if (self.widgetModel?.headLockState == "1") {
            lockState = "已关锁"
        }
        return lockState
    }
    
    var isFirstTboxStyle : Bool {
        /*
         是否展示一代车的样式
         1.1代车
         2.2代燃油车 不显示剩余燃油数量 (K线车)
         */
        return (CFWidgetData.shared.widgetModel?.type == 2 && CFWidgetData.shared.widgetModel?.energyType == 1 && CFWidgetData.shared.widgetModel?.remainOil == 0) || CFWidgetData.shared.widgetModel?.type == 1
    }
}
