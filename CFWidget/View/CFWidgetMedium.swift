//
//  CFWidgetMedium.swift
//  CFWidget
//
//  Created by yph on 2024/3/13.
//  Copyright © 2024 apple. All rights reserved.
//

import SwiftUI
import WidgetKit

@available(iOS 14.0.0, *)
struct CFWidgetMedium: View {
    var body: some View {
        ZStack {
            BgColorView()
            if (CFWidgetData.shared.widgetModel?.isDemoVehicle == "0") {
                HStack(spacing: 0) {
                    VStack {
                        createTopView()
                            .padding(EdgeInsets(top: 10, leading: 15, bottom: 0, trailing: 0))
                            .frame(height: CFWidgetData.shared.isFirstTboxStyle ? 18 : 60)
                        if (CFWidgetData.shared.isFirstTboxStyle){
                            createBtnView().padding(EdgeInsets(top: 35, leading: 15, bottom: 0, trailing: 0))
                            Spacer()
                            AddressView().padding(EdgeInsets(top: 0, leading: 5, bottom: 0, trailing: 0))
                        }else {
                            Spacer()
                            createBtnView().padding(EdgeInsets(top: 0, leading: 15, bottom: 15, trailing: 0))
                        }
                    }
                    ProductView()
                        .padding(EdgeInsets(top: 11, leading: 0, bottom: 5, trailing: 0))
                }
            } else {
                noCarView()
            }
        }
    }
}

//创建
@available(iOS 14.0.0, *)
struct createTopView : View {
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 4) {
                switch family {
                case .systemSmall:
                    //时间
                    HStack {
                        timeView()
                        Spacer()
                    }
                default:
                    //名字
                    Text(CFWidgetData.shared.widgetModel?.vehicleName ?? "")
                        .font(.system(size: 13))
                        .foregroundColor(Color(hexString: "#101010"))
                        .frame(width: geometry.size.width, height: 18, alignment: .leading)
                        .lineLimit(1)
                }
                HStack {
                    if (CFWidgetData.shared.widgetModel?.energyType == 2) {//电车里程
                        switch family {//燃油油量
                        case .systemSmall:
                            mileageView_Electro()
                                .frame(width: 103,height: 25)
                        default:
                            mileageView_Electro()
                                .frame(width: 103,height: 25)
                        }
                    } else if (!CFWidgetData.shared.isFirstTboxStyle) {
                        switch family {//燃油油量
                        case .systemSmall:
                            mileageView()
                                .frame(width: 103, height: 27)
                        default:
                            mileageView()
                                .frame(width: 103, height: 27)
                        }
                    }
                    Spacer()
                }
                .padding(.bottom,6)
                if (family == .systemSmall && (CFWidgetData.shared.widgetModel?.checkVehicleFunctionSign12 == 1 || CFWidgetData.shared.widgetModel?.checkVehicleFunctionSign1110 == 1)) {
                    //锁具状态
                    Text(CFWidgetData.shared.lockState)
                        .font(.system(size: 11))
                        .foregroundColor(Color(hexString: "#101010"))
                        .frame(width: geometry.size.width, height: 16, alignment: .leading)
                        .lineLimit(1)
                }
            }
        }
    }
}

//时间
@available(iOS 14.0.0, *)
struct timeView:View {
    var body: some View {
        Text(String(format: "更新于：%@", CFWidgetData.shared.time))
            .font(.system(size: 10))
            .foregroundColor(Color(hexString: "#888888"))
            .frame(height: 14)
    }
}

//里程-油车
@available(iOS 14.0.0, *)
struct mileageView:View {
    var body: some View {
        VStack(spacing: 2) {
            HStack(alignment: .center,spacing: 10) {
                Image(CFWidgetData.shared.widgetModel?.remainingOil ?? 0 == 0 ? "widget_oil_red" : "widget_oil_blue")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 14, height: 14, alignment: .center)
                HStack(spacing: 0) {
                    let remainingOil = CFWidgetData.shared.widgetModel?.simRemainingDays == 0 ? "--" : String(format: "%ld", CFWidgetData.shared.widgetModel?.remainingOil ?? 0)
                    Text(remainingOil)
                        .font(.system(size: 13, weight:.semibold))
                        .foregroundColor(Color(hexString: "#000000"))
                        .frame(height: 18)
                    
                    let totalOilNum = String(format: "/%ld", CFWidgetData.shared.widgetModel?.totalOilNum ?? 0)
                    Text(totalOilNum)
                        .font(.system(size: 13, weight:.semibold))
                        .foregroundColor(Color(hexString: "#999999"))
                        .frame(height: 18)
                }.padding(.top,2)
                Spacer()
            }
            
            GeometryReader { geometry in
                ZStack (alignment: .leading) {
                    progressBarView(colorStr: "C8DBDC").frame(height: 5)
                    let scale = CFWidgetData.shared.widgetModel?.simRemainingDays == 0 ? CGFloat(0) : CGFloat(Double(CFWidgetData.shared.widgetModel?.remainingOil ?? 0)/Double(CFWidgetData.shared.widgetModel?.totalOilNum ?? 0))
                    progressBarView(colorStr: "00C9D4").frame(width: geometry.size.width*scale, height: 5)
                }
            }
        }
    }
}

//里程-电车
@available(iOS 14.0.0, *)
struct mileageView_Electro:View {
    var body: some View {
        VStack (spacing: 0) {
            HStack(alignment: .lastTextBaseline ,spacing: 0) {
                Text(CFWidgetData.shared.hmiRidableMile ?? "--")
                    .font(.system(size: 14, weight:.medium))
                    .foregroundColor(Color(hexString: "#101010"))
                    .frame(height: 20,alignment: .leading)
                Text("km")
                    .font(.system(size: 10, weight:.medium))
                    .foregroundColor(Color(hexString: "#101010"))
                    .frame(height: 14, alignment: .leading)
                Text(CFWidgetData.shared.widgetModel?.bmsSoc ?? "0")
                    .font(.system(size: 14, weight:.medium))
                    .foregroundColor(Color(hexString: "#101010"))
                    .frame(height: 20,alignment: .leading)
                    .padding(.leading,15)
                Text("%")
                    .font(.system(size: 10, weight:.medium))
                    .foregroundColor(Color(hexString: "#101010"))
                    .frame(height: 14, alignment: .leading)
                Spacer()
            }
            ZStack (alignment: .leading) {
                GeometryReader { geometry in
                    progressBarView(colorStr: "C8DBDC").frame(height: 5)
                    let bmsFloat = Float(CFWidgetData.shared.widgetModel?.bmsSoc ?? "0.0")
                    let showColorStr = bmsFloat! < 20.0 ? "#FF6060" : "00C9D4"
                    progressBarView(colorStr: showColorStr).frame(width: geometry.size.width * CGFloat(bmsFloat!) / 100.0, height: 5)
                }
            }
        }
    }
}

//进度条
@available(iOS 14.0.0, *)
struct progressBarView:View {
    var colorStr :String
    var body: some View {
        Color(hexString: colorStr)
            .cornerRadius(1)
    }
}

//按钮
@available(iOS 14.0.0, *)
struct createBtnView : View {
    var body: some View {
        HStack(spacing: 15) {
            let functionArray = CFWidgetData.shared.functionArray
            let count = functionArray?.count ?? 0
            if count > 0 {
                let dict1 = functionArray![0]
                createButtonAndName(btnImage: dict1["image"]!, btnUrl: dict1["url"]!)
            }
            if count > 1 {
                let dict2 = functionArray![1]
                createButtonAndName(btnImage: dict2["image"]!, btnUrl: dict2["url"]!)
            }
            Spacer()
        }
    }
}

@available(iOS 14.0.0, *)
struct createButtonAndName : View {
    var btnImage :String//按钮图片
    var btnUrl :String//跳转链接
    
    var body: some View {
        Link(destination: URL(string: btnUrl)!) {
            VStack {
                Image(btnImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 44, height: 44, alignment: .center)
            }
            .frame(width: 44, height:44)
        }
    }
}
//地址图标+地址
@available(iOS 14.0.0, *)
struct AddressView: View {
    @Environment(\.widgetFamily) var family
    var body: some View {
        HStack(alignment: .top,spacing: 5) {
            let address = CFWidgetData.shared.widgetModel?.simRemainingDays == 0 ? "车辆位置获取失败" : CFWidgetData.shared.widgetModel?.address ?? "车辆位置获取失败"
            Image("widget_location")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 12, height: 14, alignment: .center)
                .padding(EdgeInsets(top: -1, leading: 10, bottom: 0, trailing: 0))
            
            //地理位置
            Text(address)
                .font(.system(size: 10))
                .foregroundColor(Color(hexString: "#414141"))
                .frame(alignment: .center)
                .padding(EdgeInsets(top: 0, leading: 0, bottom: 10, trailing: 10))
                .lineLimit(2)
            if (CFWidgetData.shared.isFirstTboxStyle && family != .systemSmall) {
                Spacer(minLength: 0)
            }
        }
    }
}

//车
@available(iOS 14.0.0, *)
struct ProductView:View {
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                HStack {
                    Spacer()
                    timeView().padding(.trailing, 15)
                }
                
                HStack() {
                    Spacer(minLength: 0)
                    Image(uiImage: loadNetworkImage(imgUrlString:CFWidgetData.shared.widgetModel?.vehiclePicUrl ?? "", placeholder: UIImage(named: "widget_placeholder_logo")!)!)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .trailing)
                        .padding(EdgeInsets(top: 0, leading: 0, bottom: !CFWidgetData.shared.isFirstTboxStyle ? 0 : 28, trailing: 5))
                }
                if (!CFWidgetData.shared.isFirstTboxStyle) {
                    AddressView()
                }
//                HStack(spacing: 5) {
//                    let address = CFWidgetData.shared.widgetModel?.simRemainingDays == 0 ? "车辆位置获取失败" : CFWidgetData.shared.widgetModel?.address ?? "车辆位置获取失败"
//                    let addressHeight = address.boundingRect(with:CGSize(width: geometry.size.width-37, height:CGFloat(MAXFLOAT)), options: .usesLineFragmentOrigin, attributes: [.font:UIFont.systemFont(ofSize: 10)], context:nil).size.height
//                    
//                    let imageToTop = CGFloat(addressHeight) > 20 ? -12 : -7;
//                    let textToBottom = CGFloat(addressHeight) > 20 ? 0 : 6;
//                    
//                    Image("widget_location")
//                        .resizable()
//                        .aspectRatio(contentMode: .fit)
//                        .frame(width: 12, height: 14, alignment: .center)
//                        .padding(EdgeInsets(top: CGFloat(imageToTop), leading: 10, bottom: 0, trailing: 0))
//                    
//                    //地理位置
//                    Text(address)
//                        .font(.system(size: 10))
//                        .foregroundColor(Color(hexString: "#414141"))
//                        .frame(alignment: .center)
//                        .padding(EdgeInsets(top: 0, leading: 0, bottom: CGFloat(textToBottom), trailing: 10))
//                        .lineLimit(2)
//                }
            }
        }
    }
}

//车
@available(iOS 14.0.0, *)
struct ProductView_Small:View {
    var body: some View {
        HStack() {
            Image(uiImage: loadNetworkImage(imgUrlString:CFWidgetData.shared.widgetModel?.vehiclePicUrl ?? "", placeholder: UIImage(named: "widget_placeholder_logo")!)!)
                .resizable()
                .aspectRatio(contentMode: .fill)
//                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .frame(width: 126,height: 80,alignment: .center)
        }
    }
}
// 添加图片尺寸调整方法
func resizeImage(_ image: UIImage, targetWidth: CGFloat) -> UIImage? {
    let scale = targetWidth / image.size.width
    let targetHeight = image.size.height * scale
    let size = CGSize(width: targetWidth, height: targetHeight)
    
    UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
    image.draw(in: CGRect(origin: .zero, size: size))
    let newImage = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()
    return newImage
}
//加载网络图片
@available(iOS 14.0.0, *)
func loadNetworkImage(imgUrlString:String, placeholder:UIImage) ->UIImage? {
    var image: UIImage? = placeholder
    if imgUrlString == "" {
        return image
    }
    if let iamgeData = try? Data(contentsOf: URL(string: imgUrlString)!) {
        if let originalImage = UIImage(data: iamgeData) {
                   // 添加图片压缩逻辑--- 系统对图片大小和尺寸做了限制，过大 iOS16会出现显示异常
                   image = resizeImage(originalImage, targetWidth: 300)
               }
    }
    return image;
}

//无车用户
@available(iOS 14.0.0, *)
struct noCarView:View {
    var body: some View {
        VStack(spacing: 12) {
            Image("widget_logo")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 64, height: 64, alignment: .center)
            
            Text("开启春风之旅")
                .font(.system(size: 14, weight:.medium))
                .foregroundColor(Color(hexString: "#101010"))
                .multilineTextAlignment(.center)
        }.padding([.leading, .trailing], 5)
    }
}

//背景
@available(iOS 14.0.0, *)
struct BgColorView:View {
    var body: some View {
        Color.clear
            .widgetBackground(LinearGradient(gradient: Gradient(colors: [Color(hexString: "#DFF5FA"), Color(hexString: "#EEF6F8")]), startPoint: .bottom, endPoint: .top))
    }
}

//如果是iOS17，则使用containerBackground
@available(iOS 14.0.0, *)
extension View {
    func widgetBackground(_ backgroundView: some View) -> some View {
        if #available(iOS 17.0, *) {
            return containerBackground(for: .widget) {
                backgroundView
            }
        } else {
            return background(backgroundView)
        }
    }
}
