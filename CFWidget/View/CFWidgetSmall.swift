//
//  CFWidgetSmall.swift
//  CFWidget
//
//  Created by yph on 2024/3/13.
//  Copyright © 2024 apple. All rights reserved.
//

import SwiftUI
import WidgetKit

@available(iOS 14.0.0, *)
struct CFWidgetSmall: View{
    var body: some View {
        ZStack {
            BgColorView()
            if (CFWidgetData.shared.widgetModel?.isDemoVehicle == "0") {
                VStack {
                    if (CFWidgetData.shared.isFirstTboxStyle){
                        //1代车 || 2代车K线车不展示燃油
                        HStack {
                            timeView()
                            Spacer()
                        }.padding(EdgeInsets(top: 11, leading: 15, bottom: 0, trailing: 15))
                    }else {
                        createTopView()
                            .padding(EdgeInsets(top: 11, leading: 15, bottom: 0, trailing: 15))
                    }
                    if (CFWidgetData.shared.isFirstTboxStyle){
                        //1代车 || 2代车K线车不展示燃油
                        ProductView_Small()
                            .padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 20))
                    }
                    Spacer()
                    HStack {
                        AddressView()
                            .padding(EdgeInsets(top: 0, leading: 5, bottom: 0, trailing: 5))
                        Spacer(minLength: 0)
                    }
                }
            } else {
                noCarView()
            }
        }
    }
}

