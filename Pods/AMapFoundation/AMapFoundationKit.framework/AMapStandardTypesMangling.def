//===--- AMapStandardTypesMangling - Mangling Metaprogramming ---*- C++ -*-===//
//
// This source file is part of the Swift.org open source project
//
// Copyright (c) 2014 - 2017 Apple Inc. and the Swift project authors
// Licensed under Apache License v2.0 with Runtime Library Exception
//
// See https://swift.org/LICENSE.txt for license information
// See https://swift.org/CONTRIBUTORS.txt for the list of Swift project authors
//
//===----------------------------------------------------------------------===//

/// STANDARD_TYPE(KIND, MANGLING, TYPENAME)
///   The 1-character MANGLING for a known TYPENAME of KIND.

STANDARD_TYPE(Structure, A, AutoreleasingUnsafeMutablePointer)
STANDARD_TYPE(Structure, a, Array)
STANDARD_TYPE(Structure, b, Bool)
STANDARD_TYPE(Structure, c, UnicodeScalar)
STANDARD_TYPE(Structure, D, Dictionary)
STANDARD_TYPE(Structure, d, Double)
STANDARD_TYPE(Structure, f, Float)
STANDARD_TYPE(Structure, h, Set)
STANDARD_TYPE(Structure, I, DefaultIndices)
STANDARD_TYPE(Structure, i, Int)
STANDARD_TYPE(Structure, J, Character)
STANDARD_TYPE(Structure, N, ClosedRange)
STANDARD_TYPE(Structure, n, Range)
STANDARD_TYPE(Structure, O, ObjectIdentifier)
STANDARD_TYPE(Structure, P, UnsafePointer)
STANDARD_TYPE(Structure, p, UnsafeMutablePointer)
STANDARD_TYPE(Structure, R, UnsafeBufferPointer)
STANDARD_TYPE(Structure, r, UnsafeMutableBufferPointer)
STANDARD_TYPE(Structure, S, String)
STANDARD_TYPE(Structure, s, Substring)
STANDARD_TYPE(Structure, u, UInt)
STANDARD_TYPE(Structure, V, UnsafeRawPointer)
STANDARD_TYPE(Structure, v, UnsafeMutableRawPointer)
STANDARD_TYPE(Structure, W, UnsafeRawBufferPointer)
STANDARD_TYPE(Structure, w, UnsafeMutableRawBufferPointer)

STANDARD_TYPE(Enum, q, Optional)

STANDARD_TYPE(Protocol, B, BinaryFloatingPoint)
STANDARD_TYPE(Protocol, E, Encodable)
STANDARD_TYPE(Protocol, e, Decodable)
STANDARD_TYPE(Protocol, F, FloatingPoint)
STANDARD_TYPE(Protocol, G, RandomNumberGenerator)
STANDARD_TYPE(Protocol, H, Hashable)
STANDARD_TYPE(Protocol, j, Numeric)
STANDARD_TYPE(Protocol, K, BidirectionalCollection)
STANDARD_TYPE(Protocol, k, RandomAccessCollection)
STANDARD_TYPE(Protocol, L, Comparable)
STANDARD_TYPE(Protocol, l, Collection)
STANDARD_TYPE(Protocol, M, MutableCollection)
STANDARD_TYPE(Protocol, m, RangeReplaceableCollection)
STANDARD_TYPE(Protocol, Q, Equatable)
STANDARD_TYPE(Protocol, T, Sequence)
STANDARD_TYPE(Protocol, t, IteratorProtocol)
STANDARD_TYPE(Protocol, U, UnsignedInteger)
STANDARD_TYPE(Protocol, X, RangeExpression)
STANDARD_TYPE(Protocol, x, Strideable)
STANDARD_TYPE(Protocol, Y, RawRepresentable)
STANDARD_TYPE(Protocol, y, StringProtocol)
STANDARD_TYPE(Protocol, Z, SignedInteger)
STANDARD_TYPE(Protocol, z, BinaryInteger)

#undef STANDARD_TYPE
