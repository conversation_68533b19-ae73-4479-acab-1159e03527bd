//
//  CFHomeVehicleViewController.h
//  cfmotoOnline
//
//  Created by CF_ on 2021/12/29.
//  Copyright © 2021 apple. All rights reserved.
//

#import "KLCViewController.h"
#import "CFServiceShoppingViewModel.h"
#import "CFVerhicleToolTableViewCell.h"
#import "KLCCFMainViewModel.h"
#import "CFServiceCarNewViewModel.h"
#import "CFHomeFootView.h"
#import "CFStateOverviewModel.h"
#import "CFHomeVehicleSkeletonView.h"
#import "CFCustomGifHeader.h"
#import "CFLocomotiveExhibitionHalViewController.h"
#import "CFFloatImageView.h"

NS_ASSUME_NONNULL_BEGIN

@interface CFHomeVehicleViewController : KLCViewController
@property (nonatomic, strong) CFServiceShoppingViewModel *shoppingViewModel;
@property (nonatomic, copy) NSString *adId;
@property (nonatomic, copy) NSString *forward;
@property (nonatomic, copy) NSString *webUrl;
@property (nonatomic, copy) NSString *webTitle;

@property (nonatomic, copy) NSString *productType;
//行车记录仪协议地址
@property (nonatomic, copy) NSString *dvrProtocolAddress;
@property (copy, nonatomic)  NSArray *vehiclesArray;//车辆列表 跳转车辆设置使用，提前缓存 减少卡顿
@property (nonatomic, strong) UITableView *homeListView;
@property (nonatomic, strong) NSMutableArray *vegucleListArray;
@property (nonatomic, strong) NSArray *messageListArray;
@property (nonatomic, strong) CFVehicleDetailsModel *vehicleDetailsModel;
// 是否支持 4G 解锁 （管理后台配置勾选）
@property (nonatomic, assign)  BOOL isSupport4GUnlock;
@property (nonatomic,strong) CFVerhicleToolTableViewCell *toolCell;
@property (nonatomic, strong) KLCCFMainViewModel *CFMainViewModel;
@property (nonatomic, strong) KLCVehicleReportModel *vehicleReportModel;
@property (nonatomic, assign) NSInteger redPointType;//0-无任务；1-控制器；3-仪表；9-多任务
@property(nonatomic,strong) UIImageView *showServeEndTagView;//展示服务已到期视图
@property (nonatomic, strong) CFFloatImageView *floatView;
@property (nonatomic, strong) CFServiceCarNewViewModel *carServiceModel;
@property (nonatomic, strong) NSMutableArray *getUnreadListArray;
@property (nonatomic, assign) BOOL showBleHint;//蓝牙类型错误提示
@property (nonatomic, strong) CFHomeFootView *bannerFootView;
@property (nonatomic, assign) NSInteger digitalKeyType;//数字钥匙类型：0.不支持  1.数字钥匙2.0  2.MotoPlay快速连接
@property (nonatomic, strong) CFStateOverviewModel *stateOverviewModel;
@property (nonatomic, strong) CFHomeVehicleSkeletonView *skeletonView;
//-1层展厅
@property (nonatomic, strong) CFLocomotiveExhibitionHalViewController *exhibitionVC;
@property (nonatomic, assign) BOOL isAnimationing;//是否正在动画

//位置：车辆解锁-远程解锁  4G解锁-踏板 - 8AJ0
- (void)requestRemoteUnlock;
//鉴权成功过 多辆车不管那辆车鉴权成功, App保活情况就不再鉴权
@property (nonatomic, assign) BOOL haveGaoDeLicense;
@property (nonatomic, strong)     NSURLSessionDataTask*getVehicleDataTask;

//位置：车辆解锁-远程解锁  4G解锁-踏板 - 8AJ0
//- (void)requestRemoteUnlock;

//开关锁
- (void)openLock;

//进入地图
- (void)onVehicleData;

//进入Motoplay
- (void)gotoMotoplay;

//进入我的骑行
- (void)gotoMyRideVC;

//底部功能区需要的数据
- (void)getBottomCarFuncData;

- (void)hideSkeltonView;
//4G解锁座垫锁
- (void)requestRemoteChairLock;
- (void)handleCarInfoLockData;

@end

NS_ASSUME_NONNULL_END
